package com.allcore.external.controller;

import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.Func;
import com.allcore.external.config.SgcAlarmMessagingEndpoint;
import com.allcore.external.service.ISgcBaseService;
import com.allcore.external.service.ISgcInfoService;
import com.allcore.external.vo.sgc.*;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STTabJc;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @program: bl
 * @description: sgc操作机器人对接接口类
 * @author: fanxiang
 * @create: 2025-05-07 13:56
 **/

@RestController
@AllArgsConstructor
@RequestMapping("/sgc")
@Api(value="sgc操作机器人对接接口",tags = "sgc操作机器人对接接口")
public class SgcController {

    private final ISgcBaseService sgcBaseService;
    private final ISgcInfoService sgcInfoService;
    private final SgcAlarmMessagingEndpoint sgcAlarmMessagingEndpoint;

    @GetMapping("/getRobotList")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value="获取机器人列表",notes = "")
    public R<List<InspectRobotInfoVO>>getRobotList(){

            List<InspectRobotInfoVO> list = sgcInfoService.list();
            if(Func.isNotEmpty(list)){
                return R.data(list);
            }
        return R.fail("获取机器人列表失败！");
    }

    /**
     * 同步更新机器人列表
     * @param agvIp
     * @return
     */
    @GetMapping("/syncRobotList")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value="同步更新机器人列表",notes = "")
    public R<List<InspectRobotInfoVO>>syncRobotList(@ApiParam(value = "序列号")@RequestParam(required = false) String agvCode,
                                                    @ApiParam(value = "机器人IP")@RequestParam(required = false) String agvIp,
                                                    @ApiParam(value = "机器人状态")@RequestParam(required = false) String agvStatus,
                                                    @ApiParam(value = "机器人名称") @RequestParam(required = false) String agvName,
                                                    @ApiParam(value = "ID")@RequestParam(required = false) String id){
        R<List<InspectRobotInfoVO>> robotList = sgcBaseService.getRobotList(agvCode, agvIp, agvStatus, agvName, id);
        if(robotList.isSuccess() && !robotList.getData().isEmpty()){
            return R.data(robotList.getData());
        }
        return R.fail("同步机器人列表失败！");
    }


    @GetMapping("/getRobotInfo")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value="获取机器人本体信息",notes="")
    public R<VehiclesDataVO>getRobotInfo(@ApiParam(value = "机器人IP")@RequestParam String agvIp){
        return sgcBaseService.getRobotInfo(agvIp);
    }

    @GetMapping("/getCameraInfo")
    @ApiOperationSupport(order=3)
    @ApiOperation(value="获取机器人相机信息",notes="")
    public R<List<AgvCameraInfoVO>>getCameraInfo(@ApiParam(value = "相机名称(before/after/spherical/infrared/industry/visible)")
                                                     @RequestParam(required = false) String cameraName,
                                                 @ApiParam(value = "外接云台（0:机器人相机 1：定点摄像头 2：声像仪）")@RequestParam(required = false) String ptzOuter,
                                                 @ApiParam(value = "机器人类型")@RequestParam(required = false) String type){
        return sgcBaseService.getCameraInfo(cameraName,ptzOuter,type);
    }

    @GetMapping("/getMissionRecord")
    @ApiOperationSupport(order=4)
    @ApiOperation(value="获取任务巡检记录",notes="")
    public R<List<SgcMissionRecordVO>>getMissionRecord(@ApiParam(value = "页数")@RequestParam String pageNum,
                                                       @ApiParam(value = "数量")@RequestParam String pageSize,
                                                       @ApiParam(value = "机器人IP")@RequestParam(required= false)String agvIp,
                                                       @ApiParam(value = "开始日期")@RequestParam(required= false)String startTime,
                                                       @ApiParam(value = "结束日期")@RequestParam(required= false)String endTime,
                                                       @ApiParam(value = "任务名称")@RequestParam(required= false)String missionName,
                                                       @ApiParam(value = "机器人名称")@RequestParam(required= false)String agvName,
                                                       @ApiParam(value = "任务类型")@RequestParam(required= false)String inspectionType){
        return sgcBaseService.getMissionRecord(pageNum,pageSize,agvIp,startTime,endTime,missionName,agvName,inspectionType);
    }


    @GetMapping("/getMapMarkersInfo")
    @ApiOperationSupport(order=5)
    @ApiOperation(value="查询地图标记点信息",notes="")
    public R<List<MarkersInfoVO>>getMapMarkersInfo(@ApiParam(value="机器人IP") @RequestParam String agvIp,
                                                   @ApiParam(value="地图ID") @RequestParam(required = false)String agvMapId,
                                                   @ApiParam(value="标记点") @RequestParam(required = false)String code,
                                                   @ApiParam(value="标记点类型") @RequestParam(required = false)String type){
        return sgcBaseService.getMapMarkersInfo(agvIp,agvMapId,code,type);

    }

    @GetMapping("/getPathsParams")
    @ApiOperationSupport(order=6)
    @ApiOperation(value="查询地图路径参数",notes="")
    public R<List<PathsParamsVO>>getPathsParams(@ApiParam(value="机器人IP") @RequestParam String agvIp,
                                                @ApiParam(value="地图ID") @RequestParam(required = false)String agvMapId){
        return sgcBaseService.getPathsParams(agvIp,agvMapId);
    }

    @GetMapping("/getMapDetails")
    @ApiOperationSupport(order=7)
    @ApiOperation(value="查询地图详情",notes="")
    public R<MapDetailsVO>getMapDetails(@ApiParam(value="机器人IP") @RequestParam String agvIp,
                                        @ApiParam(value="地图ID") @RequestParam String id){
        return sgcBaseService.getMapDetails(agvIp,id);
    }

    @GetMapping("/getVideoStream")
    @ApiOperationSupport(order=8)
    @ApiOperation(value="获取视频流",notes="")
    public R<String>getVideoStream(@ApiParam(value="类型(rws、visible可见、infrared红外") @RequestParam String type){
        return sgcBaseService.getVideoStream(type);
    }


    @GetMapping("/getVideoStreamNew")
    @ApiOperationSupport(order=8)
    @ApiOperation(value="获取视频流",notes="")
    public R<Map<String,String>>getVideoStreamNew(@ApiParam(value="类型(visible可见、infrared红外") @RequestParam String type, @ApiParam(value="机器人IP") @RequestParam String agvIp){

        return sgcBaseService.getVideoStreamNew(type,agvIp);
    }

    @GetMapping("/getEquipmentPoint")
    @ApiOperationSupport(order=9)
    @ApiOperation(value="获取设备点位信息",notes="")
    public R<List<EquipmentPointVO>>getEquipmentPoint(@ApiParam(value="页数") @RequestParam  String pageNum,
                                                      @ApiParam(value="数量") @RequestParam String pageSize,
                                                      @ApiParam(value="机器人IP")@RequestParam(required = false) String agvIp,
                                                      @ApiParam(value="地图ID")@RequestParam(required = false) String agvMapId,
                                                      @ApiParam(value="点位查询")@RequestParam(required = false) String pointCode){
        return sgcBaseService.getEquipmentPoint(pageNum,pageSize,agvIp,agvMapId,pointCode);
    }

    @PostMapping("/queryAlarmInfo")
    @ApiOperationSupport(order=10)
    @ApiOperation(value="发送WebSocket消息查询机器人告警信息",notes="发送包含agvIp的WebSocket消息到第三方服务，获取对应机器人的告警信息")
    public R<Boolean> queryAlarmInfo(@ApiParam(value="机器人IP", required = true) @RequestParam String agvIp){
        if(Func.isBlank(agvIp)){
            return R.fail("机器人IP不能为空");
        }

        boolean success = sgcAlarmMessagingEndpoint.queryAlarmInfo(agvIp);
        if(success){
            return R.data(true, "告警信息查询请求发送成功，请等待WebSocket返回结果");
        } else {
            return R.fail("告警信息查询请求发送失败，请检查WebSocket连接状态");
        }
    }
}
