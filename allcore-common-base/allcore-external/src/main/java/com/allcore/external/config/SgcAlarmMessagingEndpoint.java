package com.allcore.external.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.utils.Func;

import com.allcore.external.entity.AlarmInfo;
import com.allcore.external.handler.MqttMessageHandler;
import com.allcore.external.handler.WebSocketMessageHandler;
import com.allcore.external.service.IAlarmInfoService;
import com.allcore.external.vo.sgc.AlarmInfoVO;
import com.allcore.system.cache.SysCache;
import com.allcore.system.dto.DeptParamDTO;
import com.allcore.system.entity.Dept;
import com.allcore.system.feign.ISysClient;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: bl
 * @description: sgc告警消息websocket
 * @author: fanxiang
 * @create: 2025-05-07 17:35
 **/

@Slf4j
@Component
@AllArgsConstructor
public class SgcAlarmMessagingEndpoint extends AbstractMessagingEndpoint{

    @Resource
    private SgcProperties sgcProperties;

    @Resource
    private IAlarmInfoService alarmInfoService;

    @Resource
    private ISysClient sysClient;

    private final WebSocketClientManager webSocketClientManager;

    private final MongoTemplate template;

    @Override
    public String getBusinessType() {
        return "sgc";
    }

    @Override
    public String getWebSocketUrl() {
//        return "ws://***************:8093/agv/exceptionAlarm";
        return "";
    }

    @Override
    String getToken() {
        return "";
    }

    @Override
    String getTopic() {
        return "/topic/sgc";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WebSocketMessageHandler getWebSocketMessageHandler() {
        return (msg)->{
            log.info("收到服务端消息: {}", msg);
            String data="";
            if(JSON.isValid(msg)){
                // todo 转换消息类型
                JSONObject jsonObject = JSON.parseObject(msg);
                List<AlarmInfoVO> alarmInfoVOList = JSON.parseArray(jsonObject.getString("data"), AlarmInfoVO.class);

                //查询部门信息
                /*Map<String, List<Dept>> deptMap = sysClient.getDeptList(new DeptParamDTO()).stream().filter(x -> Func.notNull(x.getFullName()))
                        .collect(Collectors.groupingBy(Dept::getFullName));*/

                for (AlarmInfoVO alarmInfoVO : alarmInfoVOList) {
                    //将原始数据保存到MongoDB中
                    template.save(alarmInfoVO);
                    //todo 获取部门id
                    String deptId="";
                    /*if(deptMap.containsKey(alarmInfoVO.getSubstation())){
                        deptId=deptMap.get(alarmInfoVO.getSubstation()).get(0).getId();
                    }*/

                    AlarmInfo alarmInfo = AlarmInfo.builder().deviceType("BOOSTER")
                            .alarmLevel(alarmTransform(alarmInfoVO.getAlarmLevel()))
                            .alarmStatus(0)
                            .alarmSource("sgc_robot")
                            .alarmType(Func.isNotBlank(alarmInfoVO.getAlarmType()) ? alarmInfoVO.getAlarmType().toLowerCase() : "")
                            .alarmTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(alarmInfoVO.getAlarmTime()), ZoneId.systemDefault()))
                            .deviceId(alarmInfoVO.getEquipmentPointId())
                            .deptId(deptId).build();
                    try{
                        // 保存到mysql中
                        alarmInfoService.save(alarmInfo);
                    }catch (Exception e){
                        log.error("保存告警信息失败",e);
                    }
                }

                data = JSON.toJSONString(alarmInfoVOList);
            }else{
                data = msg;
            }
            simpMessagingTemplate.convertAndSend("/topic/sgc", data);
        };
    }

    private String alarmTransform(String alarm){
        String alarmLevel="";
        switch (alarm){
            case "EXCEPTION":
                alarmLevel= "exception";
                break;
            case "NORMAL":
                alarmLevel= "normal";
                break;
            case "WARN":
                alarmLevel= "general";
                break;
            case "SERIOUS_WARN":
                alarmLevel= "urgent";
                break;
            case "EMERGENCY_WARN":
                alarmLevel= "big";
                break;
            default:
                alarmLevel= "general";
        }
        return alarmLevel;
    }

    @Override
    MqttMessageHandler getMqttMessageHandler() {
        return null;
    }
}
