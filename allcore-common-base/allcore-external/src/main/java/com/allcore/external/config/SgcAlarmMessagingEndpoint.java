package com.allcore.external.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.utils.Func;

import com.allcore.external.entity.AlarmInfo;
import com.allcore.external.handler.MqttMessageHandler;
import com.allcore.external.handler.WebSocketMessageHandler;
import com.allcore.external.service.IAlarmInfoService;
import com.allcore.external.vo.sgc.AlarmInfoVO;
import com.allcore.system.cache.SysCache;
import com.allcore.system.dto.DeptParamDTO;
import com.allcore.system.entity.Dept;
import com.allcore.system.feign.ISysClient;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: bl
 * @description: sgc告警消息websocket
 * @author: fanxiang
 * @create: 2025-05-07 17:35
 **/

@Slf4j
@Component
@AllArgsConstructor
public class SgcAlarmMessagingEndpoint extends AbstractMessagingEndpoint{

    @Resource
    private SgcProperties sgcProperties;

    @Resource
    private IAlarmInfoService alarmInfoService;

    @Resource
    private ISysClient sysClient;

    private final WebSocketClientManager webSocketClientManager;

    private final MongoTemplate template;

    @Override
    public String getBusinessType() {
        return "sgc";
    }

    @Override
    public String getWebSocketUrl() {
        // 优先使用配置文件中的URL，如果没有配置则使用默认URL
        return Func.isNotBlank(sgcProperties.getWsAlarmUrl()) ?
               sgcProperties.getWsAlarmUrl() :
               "ws://************:8093/agv/exceptionAlarm";
    }

    @Override
    String getToken() {
        return "";
    }

    @Override
    String getTopic() {
        return "/topic/sgc";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WebSocketMessageHandler getWebSocketMessageHandler() {
        return (msg)->{
            log.info("收到服务端消息: {}", msg);
            String data="";
            if(JSON.isValid(msg)){
                // todo 转换消息类型
                JSONObject jsonObject = JSON.parseObject(msg);
                List<AlarmInfoVO> alarmInfoVOList = JSON.parseArray(jsonObject.getString("data"), AlarmInfoVO.class);

                //查询部门信息
                /*Map<String, List<Dept>> deptMap = sysClient.getDeptList(new DeptParamDTO()).stream().filter(x -> Func.notNull(x.getFullName()))
                        .collect(Collectors.groupingBy(Dept::getFullName));*/

                for (AlarmInfoVO alarmInfoVO : alarmInfoVOList) {
                    //将原始数据保存到MongoDB中
                    template.save(alarmInfoVO);
                    //todo 获取部门id
                    String deptId="";
                    /*if(deptMap.containsKey(alarmInfoVO.getSubstation())){
                        deptId=deptMap.get(alarmInfoVO.getSubstation()).get(0).getId();
                    }*/

                    AlarmInfo alarmInfo = AlarmInfo.builder().deviceType("BOOSTER")
                            .alarmLevel(alarmTransform(alarmInfoVO.getAlarmLevel()))
                            .alarmStatus(0)
                            .alarmSource("sgc_robot")
                            .alarmType(Func.isNotBlank(alarmInfoVO.getAlarmType()) ? alarmInfoVO.getAlarmType().toLowerCase() : "")
                            .alarmTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(alarmInfoVO.getAlarmTime()), ZoneId.systemDefault()))
                            .deviceId(alarmInfoVO.getEquipmentPointId())
                            .deptId(deptId).build();
                    try{
                        // 保存到mysql中
                        alarmInfoService.save(alarmInfo);
                    }catch (Exception e){
                        log.error("保存告警信息失败",e);
                    }
                }

                data = JSON.toJSONString(alarmInfoVOList);
            }else{
                data = msg;
            }
            simpMessagingTemplate.convertAndSend("/topic/sgc", data);
        };
    }

    private String alarmTransform(String alarm){
        String alarmLevel="";
        switch (alarm){
            case "EXCEPTION":
                alarmLevel= "exception";
                break;
            case "NORMAL":
                alarmLevel= "normal";
                break;
            case "WARN":
                alarmLevel= "general";
                break;
            case "SERIOUS_WARN":
                alarmLevel= "urgent";
                break;
            case "EMERGENCY_WARN":
                alarmLevel= "big";
                break;
            default:
                alarmLevel= "general";
        }
        return alarmLevel;
    }

    @Override
    MqttMessageHandler getMqttMessageHandler() {
        return null;
    }

    /**
     * 发送查询机器人告警信息的请求
     * 根据用户需求，发送包含agvIp的查询请求
     *
     * @param agvIp 机器人的IP地址
     * @return 是否发送成功
     */
    public boolean queryAlarmInfo(String agvIp) {
        if (webSocketClientManager == null) {
            log.warn("WebSocketClientManager未注入，无法发送消息");
            return false;
        }

        if (Func.isBlank(agvIp)) {
            log.warn("agvIp不能为空");
            return false;
        }

        // 确保WebSocket连接已建立
        if (!ensureConnection()) {
            log.warn("SGC WebSocket连接建立失败，无法发送查询请求");
            return false;
        }

        // 构造查询请求，根据用户需求的格式
        JSONObject request = new JSONObject();
        request.put("agvIp", agvIp);

        String clientId = getBusinessType();
        boolean success = webSocketClientManager.sendMessage(clientId, request.toJSONString());

        if (success) {
            log.info("SGC告警信息查询请求已发送，agvIp: {}, 等待服务端响应...", agvIp);
        } else {
            log.error("SGC告警信息查询请求发送失败，agvIp: {}", agvIp);
        }

        return success;
    }

    /**
     * 确保WebSocket连接已建立
     * 如果连接不存在，则主动建立连接
     *
     * @return 连接是否成功建立
     */
    public boolean ensureConnection() {
        String clientId = getBusinessType();

        // 检查连接是否已存在且有效
        if (isConnected()) {
            return true;
        }

        // 主动建立连接
        try {
            log.info("主动建立SGC WebSocket连接...");
            webSocketClientManager.addClient(clientId, getWebSocketUrl(), getToken(), getWebSocketMessageHandler());

            // 给连接一些时间建立
            Thread.sleep(1000);

            log.info("SGC WebSocket连接建立完成");
            return true;
        } catch (Exception e) {
            log.error("建立SGC WebSocket连接时发生异常", e);
            return false;
        }
    }

    /**
     * 检查WebSocket连接状态
     *
     * @return 连接是否正常
     */
    public boolean isConnected() {
        if (webSocketClientManager == null) {
            return false;
        }

        String clientId = getBusinessType();
        // 这里可以通过WebSocketClientManager检查连接状态
        // 由于WebSocketClientManager没有直接的isConnected方法，我们通过尝试获取客户端来判断
        try {
            // 简单的连接状态检查，实际实现可能需要更复杂的逻辑
            return true; // 暂时返回true，实际使用时可以根据需要完善
        } catch (Exception e) {
            log.error("检查WebSocket连接状态时发生异常", e);
            return false;
        }
    }
}
